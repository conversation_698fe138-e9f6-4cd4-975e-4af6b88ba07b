# ADC Data Extraction Validation UI - Implementation Plan

## Executive Summary

This document outlines the comprehensive implementation plan for building a user interface system that enables expert users to view, validate, and update extracted information from the ADC (Antibody Drug Conjugate) data extraction system. The UI will support role-based authentication, interactive citation management, and collaborative annotation workflows.

## 1. Technology Stack Recommendations

### Frontend Technologies
- **Framework**: React 18+ with TypeScript
  - Rationale: Component-based architecture, strong typing, excellent ecosystem
  - Alternative: Vue.js 3 with TypeScript
- **State Management**: Redux Toolkit + RTK Query
  - Rationale: Predictable state management, built-in caching, optimistic updates
- **UI Component Library**: Material-UI (MUI) v5
  - Rationale: Comprehensive components, accessibility built-in, theming support
- **Styling**: Emotion (CSS-in-JS) + MUI's styling system
- **Text Highlighting**: Rangy.js or custom implementation
- **Markdown Rendering**: react-markdown with syntax highlighting
- **Form Management**: React Hook Form + Yup validation
- **Routing**: React Router v6

### Backend Technologies
- **Runtime**: Node.js 18+ with Express.js
  - Rationale: JavaScript ecosystem consistency, excellent JSON handling
- **Authentication**: JSON Web Tokens (JWT) + bcrypt for password hashing
- **File System**: Native Node.js fs module for JSON file operations
- **Validation**: Joi or Yup for server-side validation
- **API Documentation**: Swagger/OpenAPI 3.0

### Development Tools
- **Build Tool**: Vite (faster than Create React App)
- **Testing**: Jest + React Testing Library + Cypress (E2E)
- **Code Quality**: ESLint + Prettier + Husky (pre-commit hooks)
- **Package Manager**: npm or yarn
- **Version Control**: Git with conventional commits

### Deployment & Infrastructure
- **Development**: Docker containers for consistent environment
- **Production**: Docker + nginx for static file serving
- **Database**: File-based JSON storage (as per requirements)
- **Monitoring**: Basic logging with Winston

## 2. System Architecture

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │  File System    │
│   (React SPA)   │◄──►│   (Express.js)  │◄──►│  (JSON Files)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Frontend Architecture
```
src/
├── components/           # Reusable UI components
│   ├── common/          # Generic components (Button, Modal, etc.)
│   ├── auth/            # Authentication components
│   ├── sidebar/         # Navigation sidebar components
│   ├── content/         # Main content area components
│   └── citation/        # Citation highlighting & editing
├── pages/               # Route-level components
├── hooks/               # Custom React hooks
├── store/               # Redux store configuration
├── services/            # API service layer
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
└── styles/              # Global styles and themes
```

### Backend Architecture
```
backend/
├── routes/              # Express route handlers
│   ├── auth.js         # Authentication endpoints
│   ├── publications.js # Publication data endpoints
│   └── annotations.js  # Annotation management
├── middleware/          # Express middleware
├── services/            # Business logic layer
├── utils/               # Utility functions
├── data/                # JSON file storage
│   ├── publications/    # Original extraction outputs
│   ├── annotations/     # User annotations by username
│   └── users.json       # User credentials
└── validation/          # Request validation schemas
```

## 3. Data Structure Specifications

### User Authentication Data
```json
{
  "users": [
    {
      "id": "uuid",
      "username": "string",
      "password": "hashed_password",
      "role": "admin|annotator|viewer",
      "created_at": "ISO_date",
      "last_login": "ISO_date"
    }
  ]
}
```

### Publication Data Structure
```json
{
  "publication_id": "string",
  "title": "string",
  "fulltext_markdown": "string",
  "extraction_data": [
    {
      "adc_data": { /* ADC information */ },
      "experimental_model_data": { /* Model information */ },
      "endpoints": [ /* Endpoint array */ ]
    }
  ],
  "citation_mappings": {
    "adc_citations": [
      {
        "text": "citation text",
        "start_offset": "number",
        "end_offset": "number",
        "adc_id": "string"
      }
    ],
    "model_citations": [ /* Similar structure */ ],
    "endpoint_citations": [ /* Similar structure */ ]
  }
}
```

### Annotation Data Structure
```json
{
  "publication_id": "string",
  "annotator_username": "string",
  "annotation_timestamp": "ISO_date",
  "status": "draft|submitted",
  "changes": {
    "modified_entities": [
      {
        "entity_type": "adc|model|endpoint",
        "entity_id": "string",
        "field_changes": {
          "field_name": {
            "old_value": "any",
            "new_value": "any"
          }
        }
      }
    ],
    "new_entities": [ /* New ADCs, models, endpoints */ ],
    "new_citations": [ /* New highlighted citations */ ]
  }
}
```

## 4. User Interface Design

### Layout Structure
```
┌─────────────────────────────────────────────────────────────┐
│                    Header (User Info, Logout)               │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│   Sidebar   │            Main Content Area                  │
│             │                                               │
│ - Pub Select│  ┌─────────────────────────────────────────┐  │
│ - ADC Select│  │                                         │  │
│ - Model Sel │  │        Publication Text                 │  │
│ - Filters   │  │      (with highlighted citations)       │  │
│ - Add Btns  │  │                                         │  │
│ - Save Btn  │  │                                         │  │
│             │  └─────────────────────────────────────────┘  │
│             │                                               │
└─────────────┴───────────────────────────────────────────────┘
```

### Color Scheme for Citations
- **ADC Citations**: `#E3F2FD` (Light Blue 50)
- **Experimental Model Citations**: `#E8F5E8` (Light Green 50)
- **Preclinical Endpoint Citations**: `#FFF3E0` (Light Orange 50)
- **Selected Citation**: `#FFEB3B` (Yellow 500) with border
- **Hover State**: Slightly darker shade of base color

### Component Specifications

#### Sidebar Component
- **Publication Selector**: Searchable dropdown with publication titles
- **ADC Selector**: Cascading dropdown, enabled after publication selection
- **Model Selector**: Cascading dropdown, enabled after ADC selection
- **Entity Filters**: Checkboxes for ADC/Model/Endpoint visibility
- **Action Buttons**: "Add New ADC", "Add New Model" (role-based visibility)
- **Save Controls**: Auto-save indicator, "Save Changes" button

#### Main Content Component
- **Text Renderer**: Markdown-to-HTML with citation highlighting
- **Citation Tooltip**: Hover-triggered editable data table
- **Text Selection**: Click-and-drag to create new citations
- **Context Menu**: Right-click menu for citation assignment

#### Citation Editor Modal
- **Two-Column Table**: Field Name | Extracted Value
- **Add Row Button**: For new key-value pairs
- **Validation**: Real-time field validation
- **Save/Cancel**: Modal action buttons

## 5. Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
**Objectives**: Set up development environment and basic architecture

**Tasks**:
- [ ] Initialize project structure (frontend + backend)
- [ ] Set up development environment with Docker
- [ ] Configure build tools (Vite, ESLint, Prettier)
- [ ] Implement basic Express.js server
- [ ] Create basic React app with routing
- [ ] Set up Redux store with RTK Query
- [ ] Implement user authentication (login/logout)
- [ ] Create basic layout components (Header, Sidebar, Main)

**Deliverables**:
- Working development environment
- Basic authentication system
- Skeleton UI components

### Phase 2: Data Management (Weeks 3-4)
**Objectives**: Implement data loading and basic display

**Tasks**:
- [ ] Create publication data service layer
- [ ] Implement JSON file reading/writing utilities
- [ ] Build publication selector component
- [ ] Create ADC and model cascade selectors
- [ ] Implement markdown rendering in main content
- [ ] Add basic citation highlighting (static)
- [ ] Create data transformation utilities

**Deliverables**:
- Publication data loading system
- Basic content display with static highlighting
- Navigation between publications/ADCs/models

### Phase 3: Citation Management (Weeks 5-6)
**Objectives**: Interactive citation highlighting and editing

**Tasks**:
- [ ] Implement dynamic citation highlighting
- [ ] Create citation hover tooltip component
- [ ] Build editable data table for citation details
- [ ] Add text selection for new citation creation
- [ ] Implement citation filtering based on entity types
- [ ] Create citation assignment modal
- [ ] Add validation for citation data

**Deliverables**:
- Interactive citation highlighting
- Citation editing capabilities
- New citation creation workflow

### Phase 4: Advanced Features (Weeks 7-8)
**Objectives**: Role-based features and data persistence

**Tasks**:
- [ ] Implement role-based UI restrictions
- [ ] Create "Add New ADC/Model" workflows
- [ ] Build forms for manual endpoint data entry
- [ ] Implement auto-save functionality
- [ ] Create annotation persistence system
- [ ] Add change tracking and diff visualization
- [ ] Implement submission workflow

**Deliverables**:
- Complete annotation workflow
- Data persistence system
- Role-based access control

### Phase 5: Polish & Testing (Weeks 9-10)
**Objectives**: Testing, optimization, and deployment preparation

**Tasks**:
- [ ] Comprehensive unit testing (>80% coverage)
- [ ] End-to-end testing with Cypress
- [ ] Performance optimization for large texts
- [ ] Accessibility compliance testing
- [ ] Cross-browser compatibility testing
- [ ] Error handling and user feedback
- [ ] Documentation and user guides

**Deliverables**:
- Fully tested application
- Performance optimizations
- Deployment-ready system

## 6. Technical Implementation Details

### Authentication Flow
1. User enters credentials on login page
2. Backend validates against users.json file
3. JWT token generated and returned to client
4. Token stored in localStorage/sessionStorage
5. Token included in all API requests
6. Role-based UI rendering based on token payload

### Citation Highlighting Algorithm
```javascript
// Pseudo-code for citation highlighting
function highlightCitations(text, citations) {
  // Sort citations by start offset (descending)
  const sortedCitations = citations.sort((a, b) => b.start_offset - a.start_offset);

  let highlightedText = text;

  sortedCitations.forEach(citation => {
    const before = highlightedText.substring(0, citation.start_offset);
    const citationText = highlightedText.substring(citation.start_offset, citation.end_offset);
    const after = highlightedText.substring(citation.end_offset);

    const highlightedCitation = `<span class="citation citation-${citation.type}" data-citation-id="${citation.id}">${citationText}</span>`;

    highlightedText = before + highlightedCitation + after;
  });

  return highlightedText;
}
```

### Data Persistence Strategy
- **Auto-save**: Save to draft every 30 seconds or on significant changes
- **Manual save**: User-triggered save to finalize changes
- **Version control**: Keep history of changes with timestamps
- **Conflict resolution**: Last-write-wins with user notification

### Performance Optimizations
- **Virtual scrolling**: For large publication texts
- **Lazy loading**: Load publication content on demand
- **Memoization**: Cache expensive computations
- **Debounced search**: Reduce API calls during typing
- **Code splitting**: Load components on demand

## 7. Security Considerations

### Authentication Security
- Password hashing with bcrypt (salt rounds: 12)
- JWT tokens with reasonable expiration (24 hours)
- Secure token storage (httpOnly cookies preferred over localStorage)
- Rate limiting on authentication endpoints

### Data Security
- Input validation on all user inputs
- XSS prevention through proper escaping
- CSRF protection for state-changing operations
- File system access restrictions
- User isolation for annotation files

### Access Control
- Role-based UI rendering
- Server-side permission validation
- Audit logging for data changes
- Session management and timeout

## 8. Testing Strategy

### Unit Testing
- Component testing with React Testing Library
- Service layer testing with Jest
- Utility function testing
- Redux store testing

### Integration Testing
- API endpoint testing
- Authentication flow testing
- Data persistence testing
- File system operations testing

### End-to-End Testing
- Complete user workflows
- Cross-browser compatibility
- Accessibility testing
- Performance testing

### Test Data
- Sample publications with various citation patterns
- Test user accounts for each role
- Edge cases (empty data, malformed JSON, etc.)

## 9. Deployment Strategy

### Development Environment
```dockerfile
# Dockerfile.dev
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000 3001
CMD ["npm", "run", "dev"]
```

### Production Environment
```dockerfile
# Dockerfile.prod
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
```

### Docker Compose Configuration
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    volumes:
      - ./backend:/app
      - ./data:/app/data
      - /app/node_modules
```

## 10. Maintenance and Monitoring

### Logging Strategy
- Application logs with Winston
- User action logging for audit trails
- Error tracking and reporting
- Performance monitoring

### Backup Strategy
- Regular backup of annotation files
- Version control for code changes
- Database backup procedures (if migrating from JSON)

### Update Procedures
- Rolling updates with zero downtime
- Database migration scripts
- Backward compatibility considerations

## 11. Future Enhancements

### Short-term (3-6 months)
- Advanced search and filtering
- Bulk operations for annotations
- Export functionality (PDF, Excel)
- Real-time collaboration features

### Long-term (6-12 months)
- Machine learning integration for suggestion
- Advanced analytics and reporting
- API for external integrations
- Mobile-responsive improvements

## 12. Risk Assessment and Mitigation

### Technical Risks
- **Large file performance**: Implement virtual scrolling and pagination
- **Browser compatibility**: Comprehensive testing and polyfills
- **Data corruption**: Implement backup and recovery procedures

### User Experience Risks
- **Complex interface**: User testing and iterative design
- **Learning curve**: Comprehensive documentation and training
- **Data loss**: Auto-save and recovery mechanisms

### Security Risks
- **Unauthorized access**: Strong authentication and authorization
- **Data breaches**: Encryption and access controls
- **XSS attacks**: Input sanitization and CSP headers

## 13. File Structure and Organization

### Project Root Structure
```
adc-validation-ui/
├── frontend/                 # React frontend application
│   ├── public/              # Static assets
│   ├── src/                 # Source code
│   ├── package.json         # Frontend dependencies
│   ├── vite.config.ts       # Vite configuration
│   └── tsconfig.json        # TypeScript configuration
├── backend/                 # Express.js backend
│   ├── src/                 # Source code
│   ├── data/                # JSON data storage
│   ├── package.json         # Backend dependencies
│   └── tsconfig.json        # TypeScript configuration
├── docker-compose.yml       # Development environment
├── docker-compose.prod.yml  # Production environment
├── README.md               # Project documentation
└── docs/                   # Additional documentation
```

### Frontend Detailed Structure
```
frontend/src/
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── ProtectedRoute.tsx
│   │   └── UserProfile.tsx
│   ├── sidebar/
│   │   ├── PublicationSelector.tsx
│   │   ├── ADCSelector.tsx
│   │   ├── ModelSelector.tsx
│   │   ├── EntityFilters.tsx
│   │   └── SaveControls.tsx
│   ├── content/
│   │   ├── PublicationViewer.tsx
│   │   ├── CitationHighlighter.tsx
│   │   └── TextSelector.tsx
│   ├── citation/
│   │   ├── CitationTooltip.tsx
│   │   ├── CitationEditor.tsx
│   │   └── CitationModal.tsx
│   └── common/
│       ├── Button.tsx
│       ├── Modal.tsx
│       ├── LoadingSpinner.tsx
│       └── ErrorBoundary.tsx
├── pages/
│   ├── LoginPage.tsx
│   ├── DashboardPage.tsx
│   └── AnnotationPage.tsx
├── hooks/
│   ├── useAuth.ts
│   ├── usePublication.ts
│   ├── useCitations.ts
│   └── useAutoSave.ts
├── store/
│   ├── index.ts
│   ├── authSlice.ts
│   ├── publicationSlice.ts
│   └── annotationSlice.ts
├── services/
│   ├── api.ts
│   ├── authService.ts
│   ├── publicationService.ts
│   └── annotationService.ts
├── types/
│   ├── auth.ts
│   ├── publication.ts
│   ├── annotation.ts
│   └── api.ts
├── utils/
│   ├── citationUtils.ts
│   ├── textUtils.ts
│   ├── validationUtils.ts
│   └── constants.ts
└── styles/
    ├── theme.ts
    ├── globals.css
    └── components.css
```

### Backend Detailed Structure
```
backend/src/
├── routes/
│   ├── auth.ts
│   ├── publications.ts
│   ├── annotations.ts
│   └── users.ts
├── middleware/
│   ├── auth.ts
│   ├── validation.ts
│   ├── errorHandler.ts
│   └── cors.ts
├── services/
│   ├── authService.ts
│   ├── publicationService.ts
│   ├── annotationService.ts
│   └── fileService.ts
├── utils/
│   ├── jwt.ts
│   ├── bcrypt.ts
│   ├── fileUtils.ts
│   └── logger.ts
├── validation/
│   ├── authSchemas.ts
│   ├── publicationSchemas.ts
│   └── annotationSchemas.ts
├── types/
│   ├── auth.ts
│   ├── publication.ts
│   └── annotation.ts
└── data/
    ├── publications/
    │   └── sample_publication.json
    ├── annotations/
    │   ├── admin_x/
    │   ├── annotator_y/
    │   └── viewer_z/
    └── users.json
```

## 14. API Specification

### Authentication Endpoints
```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/profile
```

### Publication Endpoints
```
GET  /api/publications              # List all publications
GET  /api/publications/:id          # Get specific publication
GET  /api/publications/:id/adcs     # Get ADCs for publication
GET  /api/publications/:id/models   # Get models for ADC
```

### Annotation Endpoints
```
GET    /api/annotations/:pubId                    # Get user's annotations
POST   /api/annotations/:pubId                    # Create/update annotations
PUT    /api/annotations/:pubId/submit             # Submit final annotations
DELETE /api/annotations/:pubId                    # Delete draft annotations
GET    /api/annotations/:pubId/history            # Get annotation history
```

### User Management Endpoints (Admin only)
```
GET    /api/users                   # List all users
POST   /api/users                   # Create new user
PUT    /api/users/:id               # Update user
DELETE /api/users/:id               # Delete user
```

## Conclusion

This comprehensive implementation plan provides a detailed roadmap for building a robust, user-friendly interface for the ADC data extraction validation system. The phased approach ensures steady progress while maintaining quality and security standards. The technology choices prioritize modern web standards, accessibility, and maintainability.

Key success factors include:
- **User-centric design**: Focus on intuitive workflows for domain experts
- **Robust architecture**: Scalable and maintainable codebase
- **Security first**: Comprehensive security measures throughout
- **Quality assurance**: Extensive testing at all levels
- **Performance optimization**: Efficient handling of large documents

The estimated timeline of 10 weeks allows for thorough development, testing, and refinement. Regular checkpoints and deliverables ensure project visibility and allow for course corrections as needed.

This plan serves as a living document that should be updated as requirements evolve and new insights emerge during development.
