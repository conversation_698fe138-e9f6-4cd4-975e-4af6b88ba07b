**Background Context:**
We built a structured data extraction system for Antibody Drug Conjugates focused preclinical study publications that operates in three sequential modules:
1. **ADC Extraction Module**: Identifies and extracts Antibody Drug Conjugates (ADCs) from full-text content
2. **Experimental Model Module**: Extracts information about experimental models tested on each identified ADC
3. **Preclinical Endpoints Module**: Extracts information about preclinical endpoints tested on each experimental model for each ADC

Each module uses an agentic system powered by large language models (LLMs) and custom built tools for tool calling. All agentic systems employ test-time scaling theory in design to increase extraction accuracy. The system currently supports article and review type publications only. Note that the codebase for this system is not currently available in this folder.

**Detailed Requirements:**
You will be provided with a sample output from this system. Now we need to build a user interface (UI) for this system so that expert users can view, validate or update the extracted information. Your responsibility is to plan and design a user interface (UI) for this module with following features:
1. Implement user authentication using credentials stored in a JSON file. Each user will have a unique username, password, and assigned role (admin, annotator, or viewer).

2. Design the overall layout with a left sidebar for navigation and a main content area for publication display:
    - The sidebar should allow users to select a publication identifier, which then enables a dropdown to select available ADCs within that publication. Selecting an ADC enables a dropdown for available experimental models associated with that ADC.
    - Include entity type filters (ADC, experimental model, preclinical endpoints) as checkboxes in the sidebar to allow users to filter and view specific citation types.
    - Provide "Add New ADC" and "Add New Experimental Model" buttons in the dropdown menus for annotators and admins to add new citations along with endpoints.

3. In the main content area, display the full-text publication in markdown format. Highlight citations for different extracted entities using distinct colors:
    - ADC citations: light blue
    - Experimental model citations: light green
    - Preclinical endpoint citations: light orange

4. Enable filtering of highlighted citations in the main area based on the entity type selections in the sidebar.

5. When a user hovers over a highlighted citation, display the extracted information as an editable two-column table (Field Name | Extracted Value). Users should be able to modify existing values, add new key-value pairs, and validate or update the extracted information as needed.

6. Users should be able to highlight new citations in the publication text by selecting a relevant text span and assigning it to an entity type (ADC, experimental model, preclinical endpoint). They can also add new key-value pairs to represent the extracted information from this new citation. This feature should be available only for annotators and admins. This feature should be present for both existing ADC-experimental model combinations and for newly added ADC-experimental model combinations.

7. For newly added ADCs or experimental models (when the extraction system missed them), display the main area without highlighted text and provide forms for annotators to manually input preclinical endpoint data for the new ADC-experimental model combination.

8. Include a "Save Changes" button in the sidebar to allow users to save validated, updated or added information. Implement auto-save for draft changes and manual save for final submission.

9. Upon submission, save the updated or newly added information in a separate JSON file under `annotations_directory/{username}/`, using the same filename as the original extracted JSON. After submission, disable editing for annotators and present the data in a read-only view.

**Technical Requirements:**
- Responsive web design compatible with modern browsers
- Fast rendering for large publication texts
- Efficient data loading and caching
- Clear visual feedback for user actions
- Accessibility compliance (WCAG 2.1 AA)

**Expected Deliverables:**
1. Complete UI/UX design
2. Technical architecture plan
3. Implementation roadmap with technology stack recommendations
4. Sample data structure specifications for JSON files
5. User workflow documentation

Please provide a detailed implementation plan including technology choices, file structure, and stage wise development approach. Save the plan in a file named `implementation_plan.md` in docs folder.