# ADC Data Extraction Validation UI - Implementation Plan

## Executive Summary

This document outlines the comprehensive implementation plan for building a user interface system that enables expert users to view, validate, and update extracted information from the ADC (Antibody Drug Conjugate) data extraction system. The UI will support role-based authentication, interactive citation management, and collaborative annotation workflows.

## 1. Technology Stack Recommendations

### Frontend Technologies
- **Framework**: React 18+ with TypeScript
  - Rationale: Component-based architecture, strong typing, excellent ecosystem
  - Alternative: Vue.js 3 with TypeScript
- **State Management**: Redux Toolkit + RTK Query
  - Rationale: Predictable state management, built-in caching, optimistic updates
- **UI Component Library**: Material-UI (MUI) v5
  - Rationale: Comprehensive components, accessibility built-in, theming support
- **Styling**: Emotion (CSS-in-JS) + MUI's styling system
- **Text Highlighting**: Rangy.js or custom implementation
- **Markdown Rendering**: react-markdown with syntax highlighting
- **Form Management**: React Hook Form + Yup validation
- **Routing**: React Router v6

### Backend Technologies
- **Framework**: FastAPI with Python 3.9+
  - Rationale: Native Pydantic integration, excellent performance, automatic API docs
  - Direct reuse of existing Pydantic models from extraction system
- **Authentication**: FastAPI-Users or custom JWT implementation with passlib
- **File System**: Python pathlib + aiofiles for async JSON operations
- **Validation**: Built-in Pydantic validation (reusing existing models)
- **API Documentation**: Automatic OpenAPI 3.0 generation with FastAPI
- **ASGI Server**: Uvicorn for production deployment

### Development Tools
- **Build Tool**: Vite (faster than Create React App)
- **Testing**: Jest + React Testing Library + Cypress (E2E)
- **Code Quality**: ESLint + Prettier + Husky (pre-commit hooks)
- **Package Manager**: npm or yarn
- **Version Control**: Git with conventional commits

### Deployment & Infrastructure
- **Development**: Docker containers for consistent environment
- **Production**: Docker + nginx for static file serving
- **Database**: File-based JSON storage (as per requirements)
- **Monitoring**: Basic logging with Winston

## 2. System Architecture

### High-Level Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │  File System    │
│   (React SPA)   │◄──►│   (Express.js)  │◄──►│  (JSON Files)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Frontend Architecture
```
src/
├── components/           # Reusable UI components
│   ├── common/          # Generic components (Button, Modal, etc.)
│   ├── auth/            # Authentication components
│   ├── sidebar/         # Navigation sidebar components
│   ├── content/         # Main content area components
│   └── citation/        # Citation highlighting & editing
├── pages/               # Route-level components
├── hooks/               # Custom React hooks
├── store/               # Redux store configuration
├── services/            # API service layer
├── types/               # TypeScript type definitions
├── utils/               # Utility functions
└── styles/              # Global styles and themes
```

### Backend Architecture
```
backend/
├── app/                 # FastAPI application
│   ├── routers/        # API route handlers
│   │   ├── auth.py     # Authentication endpoints
│   │   ├── publications.py # Publication data endpoints
│   │   └── annotations.py  # Annotation management
│   ├── middleware/     # FastAPI middleware
│   ├── services/       # Business logic layer
│   ├── models/         # Pydantic models (reused from extraction system)
│   ├── core/           # Configuration and security
│   └── dependencies/   # Dependency injection
├── data/               # JSON file storage
│   ├── publications/   # Original extraction outputs
│   ├── annotations/    # User annotations by username
│   └── users.json      # User credentials
└── tests/              # Test files
```

## 3. Data Structure Specifications

### User Authentication Data
```json
{
  "users": [
    {
      "id": "uuid",
      "username": "string",
      "password": "hashed_password",
      "role": "admin|annotator|viewer",
      "created_at": "ISO_date",
      "last_login": "ISO_date"
    }
  ]
}
```

### Publication Data Structure
Based on the actual structure in `sample_data/sample_extraction_output.json`:

```json
{
  "publication_id": "string",
  "title": "string",
  "fulltext_markdown": "string",
  "extraction_data": [
    {
      "adc_data": {
        "citations": [
          "Promiximab-duocarmycin, a new CD56 antibody-drug conjugates, is highly efficacious in small cell lung cancer xenograft models",
          "Mass spectrometry analysis showed promiximab-DUBA had an average DAR (Drug-to-Antibody Ratio) of about 2.04"
        ],
        "adc_name": "promiximab-DUBA",
        "adc_identifier": "promiximab-duocarmycin",
        "drug_antibody_ratio": "2.04",
        "conjugation_method": "Cysteine Conjugation",
        "antibody_name": "promiximab",
        "antibody_clonality": "Monoclonal Antibody (mAb)",
        "antibody_species": "Humanized",
        "antibody_isotype": "IgG",
        "payload_name": "duocarmycin",
        "payload_mechanism_of_action": "DNA alkylation",
        "linker_name": "duocarmycin-linker",
        "linker_type": "Cleavable Linker",
        "target_antigen": "CD56"
      },
      "experimental_model_data": {
        "citations": [
          "SCLC cell lines NCI-H526, NCI-H524 and NCI-H69, with IC50 values of 0.07 nmol/L, 0.18 nmol/L and 0.29 nmol/L, respectively"
        ],
        "model_identifier": "NCI-H526",
        "model_type": "Cell Line Model",
        "experiment_type": "In Vitro Studies",
        "primary_cancer_type": "Small cell lung cancer",
        "cancer_subtype": "SCLC",
        "sample_size": "Not specified",
        "study_duration": "72 hours",
        "control_groups": "promiximab antibody control",
        "dosing_schedule": "single exposure",
        "administration_route": "cell culture medium"
      },
      "endpoints": [
        {
          "endpoint_name": "ANTIGEN_EXPRESSION_PERCENTAGE",
          "endpoint_data": {
            "citations": [
              "Promiximab can bind to the CD56 expressed on the surface of NCI-H526, NCI-H524 and NCI-H69 SCLC cells"
            ],
            "measured_level": "High Antigen Expression",
            "expression_percentage": "Not specified",
            "measurement_method": "flow cytometry"
          }
        },
        {
          "endpoint_name": "EC50_HALF_MAXIMAL_EFFECTIVE_CONCENTRATION",
          "endpoint_data": {
            "citations": [
              "promiximab-DUBA exerted strong inhibitory effects in NCI-H526, NCI-H524 and NCI-H69 cells, with IC50 values of 0.07 nmol/L, 0.18 nmol/L and 0.29 nmol/L, respectively"
            ],
            "measurement_method": "cell counting kit-8 (CCK-8) assay",
            "observations": [
              {
                "measured_concentration": "0.07 nmol/L",
                "measurement_timepoint": "72 hours",
                "response_parameter": "cell viability",
                "administered_dose_range": "0.02 to 1200 nmol/L",
                "curve_fit_quality": "Not specified"
              }
            ]
          }
        }
      ]
    }
  ]
}
```

### Annotation Data Structure
```json
{
  "publication_id": "string",
  "annotator_username": "string",
  "annotation_timestamp": "ISO_date",
  "status": "draft|submitted",
  "changes": {
    "modified_entities": [
      {
        "entity_type": "adc|model|endpoint",
        "entity_id": "string",
        "field_changes": {
          "field_name": {
            "old_value": "any",
            "new_value": "any"
          }
        }
      }
    ],
    "new_entities": [ /* New ADCs, models, endpoints */ ],
    "new_citations": [ /* New highlighted citations */ ]
  }
}
```

## 4. User Interface Design

### Layout Structure
```
┌─────────────────────────────────────────────────────────────┐
│                    Header (User Info, Logout)               │
├─────────────┬───────────────────────────────────────────────┤
│             │                                               │
│   Sidebar   │            Main Content Area                  │
│             │                                               │
│ - Pub Select│  ┌─────────────────────────────────────────┐  │
│ - ADC Select│  │                                         │  │
│ - Model Sel │  │        Publication Text                 │  │
│ - Filters   │  │      (with highlighted citations)       │  │
│ - Add Btns  │  │                                         │  │
│ - Save Btn  │  │                                         │  │
│             │  └─────────────────────────────────────────┘  │
│             │                                               │
└─────────────┴───────────────────────────────────────────────┘
```

### Color Scheme for Citations
- **ADC Citations**: `#E3F2FD` (Light Blue 50)
- **Experimental Model Citations**: `#E8F5E8` (Light Green 50)
- **Preclinical Endpoint Citations**: `#FFF3E0` (Light Orange 50)
- **Selected Citation**: `#FFEB3B` (Yellow 500) with border
- **Hover State**: Slightly darker shade of base color

### Component Specifications

#### Sidebar Component
Based on the actual data structure with ADC-model combinations:

- **Publication Selector**: Searchable dropdown with publication titles
- **Combination Selector**: Dropdown showing ADC-Model combinations (e.g., "promiximab-DUBA + NCI-H526")
- **Entity Filters**: Checkboxes for ADC/Model/Endpoint citation visibility
- **Action Buttons**: "Add New Combination", "Add New Endpoint" (role-based visibility)
- **Save Controls**: Auto-save indicator, "Save Changes" button

#### Data Navigation Logic
```javascript
// How the UI handles the actual data structure
const navigationLogic = {
  // Extract unique ADCs for display
  getUniqueADCs: (extractionData) => {
    const adcs = extractionData.map(combo => ({
      name: combo.adc_data.adc_name,
      identifier: combo.adc_data.adc_identifier
    }));
    return [...new Map(adcs.map(adc => [adc.identifier, adc])).values()];
  },

  // Extract unique models for display
  getUniqueModels: (extractionData) => {
    const models = extractionData.map(combo => ({
      identifier: combo.experimental_model_data.model_identifier,
      type: combo.experimental_model_data.model_type
    }));
    return [...new Map(models.map(model => [model.identifier, model])).values()];
  },

  // Get combinations for a specific ADC
  getCombinationsForADC: (extractionData, adcIdentifier) => {
    return extractionData.filter(combo =>
      combo.adc_data.adc_identifier === adcIdentifier
    );
  },

  // Format combination display name
  formatCombinationName: (combination) => {
    return `${combination.adc_data.adc_name} + ${combination.experimental_model_data.model_identifier}`;
  }
};
```

#### Main Content Component
- **Text Renderer**: Markdown-to-HTML with citation highlighting
- **Citation Tooltip**: Hover-triggered editable data table
- **Text Selection**: Click-and-drag to create new citations
- **Context Menu**: Right-click menu for citation assignment

#### Citation Editor Modal
- **Two-Column Table**: Field Name | Extracted Value
- **Add Row Button**: For new key-value pairs
- **Validation**: Real-time field validation
- **Save/Cancel**: Modal action buttons

## 5. Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)
**Objectives**: Set up development environment and basic architecture

**Tasks**:
- [ ] Initialize project structure (frontend + backend)
- [ ] Set up development environment with Docker
- [ ] Configure build tools (Vite, ESLint, Prettier)
- [ ] Implement basic Express.js server
- [ ] Create basic React app with routing
- [ ] Set up Redux store with RTK Query
- [ ] Implement user authentication (login/logout)
- [ ] Create basic layout components (Header, Sidebar, Main)

**Deliverables**:
- Working development environment
- Basic authentication system
- Skeleton UI components

### Phase 2: Data Management (Weeks 3-4)
**Objectives**: Implement data loading and basic display

**Tasks**:
- [ ] Create publication data service layer
- [ ] Implement JSON file reading/writing utilities
- [ ] Build publication selector component
- [ ] Create ADC and model cascade selectors
- [ ] Implement markdown rendering in main content
- [ ] Add basic citation highlighting (static)
- [ ] Create data transformation utilities

**Deliverables**:
- Publication data loading system
- Basic content display with static highlighting
- Navigation between publications/ADCs/models

### Phase 3: Citation Management (Weeks 5-6)
**Objectives**: Interactive citation highlighting and editing

**Tasks**:
- [ ] Implement dynamic citation highlighting
- [ ] Create citation hover tooltip component
- [ ] Build editable data table for citation details
- [ ] Add text selection for new citation creation
- [ ] Implement citation filtering based on entity types
- [ ] Create citation assignment modal
- [ ] Add validation for citation data

**Deliverables**:
- Interactive citation highlighting
- Citation editing capabilities
- New citation creation workflow

### Phase 4: Advanced Features (Weeks 7-8)
**Objectives**: Role-based features and data persistence

**Tasks**:
- [ ] Implement role-based UI restrictions
- [ ] Create "Add New ADC/Model" workflows
- [ ] Build forms for manual endpoint data entry
- [ ] Implement auto-save functionality
- [ ] Create annotation persistence system
- [ ] Add change tracking and diff visualization
- [ ] Implement submission workflow

**Deliverables**:
- Complete annotation workflow
- Data persistence system
- Role-based access control

### Phase 5: Polish & Testing (Weeks 9-10)
**Objectives**: Testing, optimization, and deployment preparation

**Tasks**:
- [ ] Comprehensive unit testing (>80% coverage)
- [ ] End-to-end testing with Cypress
- [ ] Performance optimization for large texts
- [ ] Accessibility compliance testing
- [ ] Cross-browser compatibility testing
- [ ] Error handling and user feedback
- [ ] Documentation and user guides

**Deliverables**:
- Fully tested application
- Performance optimizations
- Deployment-ready system

## 6. Technical Implementation Details

### FastAPI Advantages for ADC Data System

#### Direct Pydantic Model Reuse
```python
# Existing models can be used directly as API schemas
from utils.extraction_pydantic_models import AntibodyDrugConjugate, ExperimentalModel
from typing import List

# Data structure matching sample_extraction_output.json
class ADCModelCombination(BaseModel):
    adc_data: AntibodyDrugConjugate
    experimental_model_data: ExperimentalModel
    endpoints: List[Dict[str, Any]]  # Using existing endpoint models

class PublicationData(BaseModel):
    publication_id: str
    title: str
    fulltext_markdown: str
    extraction_data: List[ADCModelCombination]

@app.post("/api/annotations/{pub_id}/combination/{combo_index}/adc")
async def update_adc_data(
    pub_id: str,
    combo_index: int,
    adc_data: AntibodyDrugConjugate,
    current_user: User = Depends(get_current_user)
):
    # Automatic validation using existing Pydantic models
    return await annotation_service.update_adc_combination(
        pub_id, combo_index, adc_data, current_user
    )
```

#### Automatic API Documentation
FastAPI generates interactive API docs at `/docs` using your existing Pydantic models, making it easy for frontend developers to understand the data structures.

#### Type Safety and Validation
```python
# Endpoint with automatic validation
@app.get("/api/publications/{pub_id}/adcs", response_model=List[AntibodyDrugConjugate])
async def get_publication_adcs(
    pub_id: str,
    entity_filter: Optional[List[str]] = Query(None),
    current_user: User = Depends(get_current_user)
):
    # FastAPI automatically validates query parameters and response types
    return await publication_service.get_adcs(pub_id, entity_filter)
```

#### Future ML Integration Ready
```python
# Easy integration with ML models for suggestions
@app.post("/api/suggestions/endpoints")
async def suggest_endpoints(
    text: str,
    adc_context: AntibodyDrugConjugate,
    model_context: ExperimentalModel
):
    # Can easily integrate with scikit-learn, transformers, etc.
    suggestions = await ml_service.predict_endpoints(text, adc_context, model_context)
    return suggestions
```

### Authentication Flow
1. User enters credentials on login page
2. Backend validates against users.json file using FastAPI-Users or custom implementation
3. JWT token generated and returned to client
4. Token stored in localStorage/sessionStorage
5. Token included in all API requests with FastAPI's security dependencies
6. Role-based UI rendering based on token payload

### Citation Highlighting Algorithm
Based on the actual data structure where citations are text arrays within each section:

```javascript
// Extract and highlight citations from the actual data structure
function highlightCitations(fullText, extractionData) {
  let highlightedText = fullText;
  const allCitations = [];

  // Extract citations from each ADC-model combination
  extractionData.forEach((combination, combIndex) => {
    // ADC citations
    combination.adc_data.citations.forEach(citation => {
      allCitations.push({
        text: citation,
        type: 'adc',
        entityId: `adc-${combIndex}`,
        data: combination.adc_data
      });
    });

    // Model citations
    combination.experimental_model_data.citations.forEach(citation => {
      allCitations.push({
        text: citation,
        type: 'model',
        entityId: `model-${combIndex}`,
        data: combination.experimental_model_data
      });
    });

    // Endpoint citations
    combination.endpoints.forEach((endpoint, endpointIndex) => {
      endpoint.endpoint_data.citations.forEach(citation => {
        allCitations.push({
          text: citation,
          type: 'endpoint',
          entityId: `endpoint-${combIndex}-${endpointIndex}`,
          endpointName: endpoint.endpoint_name,
          data: endpoint.endpoint_data
        });
      });
    });
  });

  // Find citation positions in full text and highlight
  allCitations.forEach((citation, index) => {
    const citationIndex = highlightedText.indexOf(citation.text);
    if (citationIndex !== -1) {
      const before = highlightedText.substring(0, citationIndex);
      const after = highlightedText.substring(citationIndex + citation.text.length);

      const highlightedCitation = `<span class="citation citation-${citation.type}"
        data-citation-id="${citation.entityId}"
        data-citation-index="${index}"
        data-endpoint-name="${citation.endpointName || ''}"
        title="Click to edit ${citation.type} data">
        ${citation.text}
      </span>`;

      highlightedText = before + highlightedCitation + after;
    }
  });

  return { highlightedText, citationMap: allCitations };
}
```

### Data Persistence Strategy
- **Auto-save**: Save to draft every 30 seconds or on significant changes
- **Manual save**: User-triggered save to finalize changes
- **Version control**: Keep history of changes with timestamps
- **Conflict resolution**: Last-write-wins with user notification

### Performance Optimizations
- **Virtual scrolling**: For large publication texts
- **Lazy loading**: Load publication content on demand
- **Memoization**: Cache expensive computations
- **Debounced search**: Reduce API calls during typing
- **Code splitting**: Load components on demand

## 7. Security Considerations

### Authentication Security
- Password hashing with bcrypt (salt rounds: 12)
- JWT tokens with reasonable expiration (24 hours)
- Secure token storage (httpOnly cookies preferred over localStorage)
- Rate limiting on authentication endpoints

### Data Security
- Input validation on all user inputs
- XSS prevention through proper escaping
- CSRF protection for state-changing operations
- File system access restrictions
- User isolation for annotation files

### Access Control
- Role-based UI rendering
- Server-side permission validation
- Audit logging for data changes
- Session management and timeout

## 8. Testing Strategy

### Unit Testing
- Component testing with React Testing Library
- Service layer testing with Jest
- Utility function testing
- Redux store testing

### Integration Testing
- API endpoint testing
- Authentication flow testing
- Data persistence testing
- File system operations testing

### End-to-End Testing
- Complete user workflows
- Cross-browser compatibility
- Accessibility testing
- Performance testing

### Test Data
- Sample publications with various citation patterns
- Test user accounts for each role
- Edge cases (empty data, malformed JSON, etc.)

## 9. Deployment Strategy

### Development Environment
```dockerfile
# Frontend Dockerfile.dev
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]

# Backend Dockerfile.dev
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
```

### Production Environment
```dockerfile
# Frontend Dockerfile.prod
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80

# Backend Dockerfile.prod
FROM python:3.11-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Docker Compose Configuration
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - ./data:/app/data
    environment:
      - PYTHONPATH=/app
```

### Domino Platform Workspace Deployment

For enterprise environments using Domino Data Lab platform, the application can be deployed within workspace containers:

#### Domino Workspace Setup
```bash
# Install dependencies in Domino workspace
pip install -r requirements.txt
npm install --prefix frontend

# Set up data directories
mkdir -p /mnt/data/adc-validation/{publications,annotations,users}
```

#### Domino Environment Configuration
```dockerfile
# Custom Domino environment Dockerfile
FROM dominodatalab/base:Ubuntu20_DAD_Py3.9_R4.2_20230601
USER root

# Install Node.js for frontend
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
RUN apt-get install -y nodejs

# Install Python dependencies
COPY requirements.txt /tmp/
RUN pip install -r /tmp/requirements.txt

# Set up application directories
RUN mkdir -p /opt/adc-validation
WORKDIR /opt/adc-validation

USER ubuntu
```

#### Domino Workspace Startup Script
```bash
#!/bin/bash
# startup.sh - Place in workspace root

# Start FastAPI backend in background
cd /mnt/code/adc-validation-ui/backend
uvicorn app.main:app --host 0.0.0.0 --port 8000 &

# Start React frontend
cd /mnt/code/adc-validation-ui/frontend
npm start &

# Wait for services to start
sleep 10

echo "ADC Validation UI is starting..."
echo "Backend API: http://localhost:8000"
echo "Frontend UI: http://localhost:3000"
echo "API Documentation: http://localhost:8000/docs"

# Keep script running
wait
```

#### Domino Project Configuration
```yaml
# domino.yml - Project configuration
name: adc-validation-ui
description: ADC Data Extraction Validation Interface

compute_environment:
  name: adc-validation-env

hardware_tier:
  name: medium

data_sources:
  - name: adc-publications
    path: /mnt/data/adc-validation/publications
  - name: adc-annotations
    path: /mnt/data/adc-validation/annotations

exposed_ports:
  - 3000  # Frontend
  - 8000  # Backend API

startup_script: startup.sh
```

#### Domino Workspace Access
```python
# domino_config.py - Configuration for Domino environment
import os
from pathlib import Path

class DominoConfig:
    # Data paths in Domino workspace
    DATA_ROOT = Path("/mnt/data/adc-validation")
    PUBLICATIONS_DIR = DATA_ROOT / "publications"
    ANNOTATIONS_DIR = DATA_ROOT / "annotations"
    USERS_FILE = DATA_ROOT / "users" / "users.json"

    # Domino-specific settings
    WORKSPACE_URL = os.getenv("DOMINO_USER_HOST", "localhost")
    PROJECT_ID = os.getenv("DOMINO_PROJECT_ID", "adc-validation")

    # API URLs for Domino workspace
    BACKEND_URL = f"http://{WORKSPACE_URL}:8000"
    FRONTEND_URL = f"http://{WORKSPACE_URL}:3000"

    @classmethod
    def ensure_directories(cls):
        """Create necessary directories in Domino workspace"""
        cls.PUBLICATIONS_DIR.mkdir(parents=True, exist_ok=True)
        cls.ANNOTATIONS_DIR.mkdir(parents=True, exist_ok=True)
        (cls.DATA_ROOT / "users").mkdir(parents=True, exist_ok=True)
```

#### Domino Deployment Benefits
- **Integrated Environment**: Runs within existing Domino data science workspace
- **Data Persistence**: Leverages Domino's data storage and versioning
- **User Management**: Can integrate with Domino's user authentication system
- **Scalability**: Access to Domino's compute resources and scaling capabilities
- **Collaboration**: Multiple users can access the same workspace instance
- **Version Control**: Automatic versioning of annotations and data changes

#### Domino-Specific Considerations
- **Port Management**: Use Domino's exposed ports configuration
- **Data Access**: Leverage Domino's data source mounting
- **Authentication**: Option to integrate with Domino's SSO system
- **Resource Limits**: Configure appropriate hardware tiers for performance
- **Persistence**: Ensure data is stored in mounted volumes for persistence across workspace restarts

## 10. Maintenance and Monitoring

### Logging Strategy
- Application logs with Winston
- User action logging for audit trails
- Error tracking and reporting
- Performance monitoring

### Backup Strategy
- Regular backup of annotation files
- Version control for code changes
- Database backup procedures (if migrating from JSON)

### Update Procedures
- Rolling updates with zero downtime
- Database migration scripts
- Backward compatibility considerations

## 11. Future Enhancements

### Short-term (3-6 months)
- Advanced search and filtering
- Bulk operations for annotations
- Export functionality (PDF, Excel)
- Real-time collaboration features

### Long-term (6-12 months)
- Machine learning integration for suggestion
- Advanced analytics and reporting
- API for external integrations
- Mobile-responsive improvements

## 12. Risk Assessment and Mitigation

### Technical Risks
- **Large file performance**: Implement virtual scrolling and pagination
- **Browser compatibility**: Comprehensive testing and polyfills
- **Data corruption**: Implement backup and recovery procedures

### User Experience Risks
- **Complex interface**: User testing and iterative design
- **Learning curve**: Comprehensive documentation and training
- **Data loss**: Auto-save and recovery mechanisms

### Security Risks
- **Unauthorized access**: Strong authentication and authorization
- **Data breaches**: Encryption and access controls
- **XSS attacks**: Input sanitization and CSP headers

## 13. File Structure and Organization

### Project Root Structure
```
adc-validation-ui/
├── frontend/                 # React frontend application
│   ├── public/              # Static assets
│   ├── src/                 # Source code
│   ├── package.json         # Frontend dependencies
│   ├── vite.config.ts       # Vite configuration
│   └── tsconfig.json        # TypeScript configuration
├── backend/                 # Express.js backend
│   ├── src/                 # Source code
│   ├── data/                # JSON data storage
│   ├── package.json         # Backend dependencies
│   └── tsconfig.json        # TypeScript configuration
├── docker-compose.yml       # Development environment
├── docker-compose.prod.yml  # Production environment
├── README.md               # Project documentation
└── docs/                   # Additional documentation
```

### Frontend Detailed Structure
```
frontend/src/
├── components/
│   ├── auth/
│   │   ├── LoginForm.tsx
│   │   ├── ProtectedRoute.tsx
│   │   └── UserProfile.tsx
│   ├── sidebar/
│   │   ├── PublicationSelector.tsx
│   │   ├── ADCSelector.tsx
│   │   ├── ModelSelector.tsx
│   │   ├── EntityFilters.tsx
│   │   └── SaveControls.tsx
│   ├── content/
│   │   ├── PublicationViewer.tsx
│   │   ├── CitationHighlighter.tsx
│   │   └── TextSelector.tsx
│   ├── citation/
│   │   ├── CitationTooltip.tsx
│   │   ├── CitationEditor.tsx
│   │   └── CitationModal.tsx
│   └── common/
│       ├── Button.tsx
│       ├── Modal.tsx
│       ├── LoadingSpinner.tsx
│       └── ErrorBoundary.tsx
├── pages/
│   ├── LoginPage.tsx
│   ├── DashboardPage.tsx
│   └── AnnotationPage.tsx
├── hooks/
│   ├── useAuth.ts
│   ├── usePublication.ts
│   ├── useCitations.ts
│   └── useAutoSave.ts
├── store/
│   ├── index.ts
│   ├── authSlice.ts
│   ├── publicationSlice.ts
│   └── annotationSlice.ts
├── services/
│   ├── api.ts
│   ├── authService.ts
│   ├── publicationService.ts
│   └── annotationService.ts
├── types/
│   ├── auth.ts
│   ├── publication.ts
│   ├── annotation.ts
│   └── api.ts
├── utils/
│   ├── citationUtils.ts
│   ├── textUtils.ts
│   ├── validationUtils.ts
│   └── constants.ts
└── styles/
    ├── theme.ts
    ├── globals.css
    └── components.css
```

### Backend Detailed Structure
```
backend/
├── app/
│   ├── main.py              # FastAPI application entry point
│   ├── routers/
│   │   ├── __init__.py
│   │   ├── auth.py          # Authentication endpoints
│   │   ├── publications.py  # Publication data endpoints
│   │   ├── annotations.py   # Annotation management
│   │   └── users.py         # User management (admin only)
│   ├── middleware/
│   │   ├── __init__.py
│   │   ├── auth.py          # JWT authentication middleware
│   │   ├── cors.py          # CORS configuration
│   │   └── error_handler.py # Global error handling
│   ├── services/
│   │   ├── __init__.py
│   │   ├── auth_service.py
│   │   ├── publication_service.py
│   │   ├── annotation_service.py
│   │   └── file_service.py
│   ├── models/              # Pydantic models
│   │   ├── __init__.py
│   │   ├── auth.py          # User and token models
│   │   ├── publication.py   # Publication data models
│   │   ├── annotation.py    # Annotation models
│   │   └── extraction.py    # Reused from utils/extraction_pydantic_models.py
│   ├── core/
│   │   ├── __init__.py
│   │   ├── config.py        # Application configuration
│   │   ├── security.py      # Security utilities
│   │   └── database.py      # File-based data access
│   └── dependencies/
│       ├── __init__.py
│       ├── auth.py          # Authentication dependencies
│       └── database.py      # Database dependencies
├── data/
│   ├── publications/
│   │   └── sample_publication.json
│   ├── annotations/
│   │   ├── admin_x/
│   │   ├── annotator_y/
│   │   └── viewer_z/
│   └── users.json
├── tests/
│   ├── __init__.py
│   ├── test_auth.py
│   ├── test_publications.py
│   └── test_annotations.py
├── requirements.txt
└── pyproject.toml
```

### Sample requirements.txt
```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
aiofiles==23.2.1
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2
```

## 14. API Specification

### Authentication Endpoints
```
POST /api/auth/login
POST /api/auth/logout
POST /api/auth/refresh
GET  /api/auth/profile
```

### Publication Endpoints
```
GET  /api/publications                           # List all publications
GET  /api/publications/{pub_id}                  # Get specific publication with full extraction data
GET  /api/publications/{pub_id}/combinations     # Get all ADC-model combinations
GET  /api/publications/{pub_id}/adcs             # Get unique ADCs across all combinations
GET  /api/publications/{pub_id}/models           # Get unique models across all combinations
GET  /api/publications/{pub_id}/fulltext         # Get publication fulltext markdown
```

### Annotation Endpoints
```
GET    /api/annotations/{pub_id}                           # Get user's annotations
POST   /api/annotations/{pub_id}                           # Create/update annotations
PUT    /api/annotations/{pub_id}/submit                    # Submit final annotations
DELETE /api/annotations/{pub_id}                           # Delete draft annotations
GET    /api/annotations/{pub_id}/history                   # Get annotation history

# Specific entity updates
PUT    /api/annotations/{pub_id}/combination/{combo_index}/adc        # Update ADC data
PUT    /api/annotations/{pub_id}/combination/{combo_index}/model      # Update model data
PUT    /api/annotations/{pub_id}/combination/{combo_index}/endpoint/{endpoint_index}  # Update endpoint data

# Citation management
POST   /api/annotations/{pub_id}/citations                 # Add new citation
PUT    /api/annotations/{pub_id}/citations/{citation_id}   # Update citation
DELETE /api/annotations/{pub_id}/citations/{citation_id}   # Delete citation
```

### User Management Endpoints (Admin only)
```
GET    /api/users                   # List all users
POST   /api/users                   # Create new user
PUT    /api/users/:id               # Update user
DELETE /api/users/:id               # Delete user
```